# 楠楠家厨管理后台开发任务清单

## 📋 项目概述

**目标：** 完善 webs/admin 管理后台的用户认证系统和环境配置

**技术栈：** Vue 3 + Element Plus + Vite + Pinia

**开发周期：** 9-13天（因微信扫码问题增加2天）

---

## 🚨 紧急修复任务

### ❌ 微信扫码登录二维码问题修复
**优先级：** 🔴 高
**状态：** 待修复
**问题描述：** 微信扫码登录页面二维码显示异常

#### 🔍 问题根本原因分析：

**1. 后端API缺失** - 最关键问题
- ❌ `/auth/wechat/qrcode` 接口在后端完全未实现
- ❌ `/auth/wechat/scan-status/:key` 接口未实现
- ❌ `/auth/wechat/callback` 接口未实现
- ✅ 只有小程序微信登录接口 (`/auth/login` with `loginType: 'wechat'`)

**2. 前端组件架构问题**
- 🔄 `WechatQRLogin.vue` 调用不存在的API接口
- 🔄 `ReQrcode` 组件过于复杂，使用TSX语法
- 🔄 登录页面存在两套二维码组件（混乱）

**3. 微信登录流程不匹配**
- 🔄 管理后台需要网页版微信扫码登录
- 🔄 当前只支持小程序内微信登录
- 🔄 缺少微信公众号/开放平台配置

#### 🛠️ 修复方案：

**方案A：实现完整微信扫码登录（推荐）**
- [ ] 后端实现微信网页授权API
- [ ] 配置微信开放平台应用
- [ ] 实现二维码生成和状态轮询
- [ ] 完善前端扫码组件

**方案B：临时禁用微信扫码（快速）**
- [ ] 暂时隐藏微信扫码登录选项
- [ ] 只保留账号密码登录
- [ ] 后续再实现微信扫码功能

#### 📋 详细修复任务：

**后端API实现：**
- [ ] 创建 `webs/server/src/routes/wechatAuth.js`
- [ ] 实现微信二维码生成接口
- [ ] 实现扫码状态检查接口
- [ ] 实现微信授权回调处理
- [ ] 配置微信开放平台参数

**前端组件优化：**
- [ ] 简化 `ReQrcode` 组件或替换为更简单的库
- [ ] 修复 `WechatQRLogin` 组件API调用
- [ ] 统一登录页面的二维码显示逻辑
- [ ] 添加错误处理和降级方案

**配置和环境：**
- [ ] 添加微信开放平台配置
- [ ] 配置回调域名和授权范围
- [ ] 测试不同环境下的微信登录流程

---

## 🎯 阶段一：环境配置优化 (1-2天)

### 1.1 Package.json 脚本配置
**状态：** 📝 待开发
**负责人：** 开发者
**预计时间：** 0.5天

#### 任务详情：
- [ ] 添加多环境运行脚本
  ```json
  {
    "scripts": {
      "dev": "cross-env NODE_ENV=development vite",
      "test": "cross-env NODE_ENV=test vite", 
      "prod": "cross-env NODE_ENV=production vite",
      "build": "cross-env NODE_ENV=production vite build",
      "build:test": "cross-env NODE_ENV=test vite build"
    }
  }
  ```
- [ ] 安装 `cross-env` 依赖包
- [ ] 验证脚本在不同操作系统下的兼容性

### 1.2 环境配置文件创建
**状态：** 📝 待开发
**负责人：** 开发者
**预计时间：** 0.5天

#### 任务详情：
- [ ] 创建 `webs/admin/.env.development`
  ```env
  NODE_ENV=development
  VITE_API_BASE_URL=http://localhost:3000/api
  VITE_APP_TITLE=楠楠家厨管理系统(开发)
  VITE_DEBUG=true
  ```
- [ ] 创建 `webs/admin/.env.test`
  ```env
  NODE_ENV=test
  VITE_API_BASE_URL=http://*************:3000/api
  VITE_APP_TITLE=楠楠家厨管理系统(测试)
  VITE_DEBUG=true
  ```
- [ ] 创建 `webs/admin/.env.production`
  ```env
  NODE_ENV=production
  VITE_API_BASE_URL=http://*************:3001/api
  VITE_APP_TITLE=楠楠家厨管理系统
  VITE_DEBUG=false
  ```

### 1.3 Vite配置优化
**状态：** 📝 待开发
**负责人：** 开发者
**预计时间：** 0.5天

#### 任务详情：
- [ ] 修改 `webs/admin/vite.config.js` 支持环境变量
- [ ] 配置不同环境的代理设置
- [ ] 优化构建配置

---

## 🔐 阶段二：用户认证系统完善 (3-4天)

### 2.1 登录页面优化
**状态：** 🔄 部分完成
**负责人：** 开发者
**预计时间：** 1天

#### 当前状态：
- ✅ 基础登录页面已存在 (`webs/admin/src/views/login/index.vue`)
- ✅ 微信扫码登录组件已存在
- ❌ 二维码显示有问题（需要修复）

#### 待完成任务：
- [ ] 修复微信扫码二维码显示问题
- [ ] 完善表单验证规则
- [ ] 优化登录错误处理
- [ ] 添加登录状态持久化
- [ ] 实现"记住我"功能
- [ ] 优化登录成功后的跳转逻辑

### 2.2 注册页面实现
**状态：** 🔄 部分完成
**负责人：** 开发者
**预计时间：** 1.5天

#### 当前状态：
- ✅ 基础注册页面已存在 (`webs/admin/src/views/auth/register.vue`)
- ❌ 注册API对接未完成
- ❌ 表单验证需要完善

#### 待完成任务：
- [ ] 完善注册表单验证
  - [ ] 用户名格式验证
  - [ ] 手机号格式验证
  - [ ] 密码强度验证
  - [ ] 确认密码一致性验证
- [ ] 实现注册API对接
- [ ] 添加手机号验证码功能
- [ ] 实现注册成功自动登录
- [ ] 添加用户协议和隐私政策确认

### 2.3 找回密码功能
**状态：** 🔄 部分完成
**负责人：** 开发者
**预计时间：** 1.5天

#### 当前状态：
- ✅ 基础找回密码页面已存在 (`webs/admin/src/views/auth/forgot-password.vue`)
- ✅ 重置密码页面已存在 (`webs/admin/src/views/auth/reset-password.vue`)
- ❌ API对接未完成

#### 待完成任务：
- [ ] 实现邮箱验证找回密码
- [ ] 添加验证码发送功能
- [ ] 实现密码重置API对接
- [ ] 添加重置链接有效期验证
- [ ] 完善安全验证机制
- [ ] 优化用户体验流程

---

## 🌐 阶段三：API接口完善 (2-3天)

### 3.1 认证相关API
**状态：** 🔄 部分完成
**负责人：** 开发者
**预计时间：** 1.5天

#### 当前状态：
- ✅ 登录API已实现 (`webs/server/src/controllers/authController.js`)
- ✅ 微信登录API已实现
- ❌ 注册API需要完善
- ❌ 找回密码API未实现

#### 待完成任务：
- [ ] 完善用户注册API
  - [ ] 添加手机号验证
  - [ ] 实现验证码发送
  - [ ] 优化注册流程
- [ ] 实现找回密码API
  - [ ] 邮箱验证码发送
  - [ ] 密码重置token生成
  - [ ] 密码重置验证
- [ ] 优化登录API响应格式
- [ ] 添加token刷新机制
- [ ] 实现登录日志记录

### 3.2 微信扫码登录API实现
**状态：** ❌ 需要从零开始实现
**负责人：** 开发者
**预计时间：** 2-3天

#### 待完成任务：
- [ ] 创建微信网页授权路由文件
- [ ] 实现 `/auth/wechat/qrcode` 接口（生成登录二维码）
- [ ] 实现 `/auth/wechat/scan-status/:key` 接口（检查扫码状态）
- [ ] 实现 `/auth/wechat/callback` 接口（处理授权回调）
- [ ] 配置微信开放平台应用参数
- [ ] 实现二维码过期和刷新机制
- [ ] 添加扫码状态缓存（Redis或内存）
- [ ] 测试完整的扫码登录流程

---

## 🧪 阶段四：测试和优化 (1-2天)

### 4.1 功能测试
**状态：** 📝 待开始
**负责人：** 开发者
**预计时间：** 1天

#### 测试计划：
- [ ] 登录功能测试
  - [ ] 账号密码登录
  - [ ] 微信扫码登录
  - [ ] 登录失败处理
  - [ ] 登录状态持久化
- [ ] 注册功能测试
  - [ ] 注册表单验证
  - [ ] 注册流程完整性
  - [ ] 注册成功自动登录
- [ ] 找回密码测试
  - [ ] 邮箱验证流程
  - [ ] 密码重置流程
  - [ ] 安全验证机制
- [ ] 环境切换测试
  - [ ] 开发环境运行
  - [ ] 测试环境运行
  - [ ] 生产环境构建

### 4.2 用户体验优化
**状态：** 📝 待开始
**负责人：** 开发者
**预计时间：** 1天

#### 优化项目：
- [ ] 响应式设计优化
- [ ] 加载状态优化
- [ ] 错误提示优化
- [ ] 表单交互优化
- [ ] 页面切换动画
- [ ] 无障碍访问优化

---

## 📊 进度跟踪

### 总体进度
- **紧急修复：** 0% (0/1 完成) - 微信扫码问题
- **阶段一：** 0% (0/3 完成) - 环境配置
- **阶段二：** 20% (1/5 完成) - 认证系统
- **阶段三：** 10% (1/5 完成) - API接口（微信API需重新实现）
- **阶段四：** 0% (0/2 完成) - 测试优化
- **总进度：** 12% (2/16 完成)

### 关键里程碑
- [ ] **里程碑0：** 微信扫码问题修复 (预计第1-2天) 🔴
- [ ] **里程碑1：** 环境配置完成 (预计第3天)
- [ ] **里程碑2：** 登录功能完善 (预计第5天)
- [ ] **里程碑3：** 注册功能完成 (预计第7天)
- [ ] **里程碑4：** 找回密码完成 (预计第9天)
- [ ] **里程碑5：** 测试和优化完成 (预计第11天)

---

## 🔧 技术债务

### 代码质量问题
- [ ] ReQrcode组件过于复杂，需要简化
- [ ] 错误处理机制不统一
- [ ] 缺少TypeScript类型定义
- [ ] 组件复用性不足

### 性能优化
- [ ] 二维码生成性能优化
- [ ] 登录状态检查优化
- [ ] 页面加载速度优化
- [ ] 网络请求优化

---

## 📝 开发规范

### 代码规范
- 使用 Vue 3 Composition API
- 遵循 ESLint 配置规则
- 组件命名使用 PascalCase
- 文件命名使用 kebab-case

### 提交规范
- feat: 新功能
- fix: 修复问题
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

---

## 🚀 部署计划

### 测试环境部署
- [ ] 配置测试环境CI/CD
- [ ] 自动化测试集成
- [ ] 测试数据准备

### 生产环境部署
- [ ] 生产环境配置
- [ ] 性能监控配置
- [ ] 错误日志收集
- [ ] 备份策略制定

---

## 🚨 紧急决策建议

### 微信扫码登录问题处理方案

**当前情况：** 微信扫码登录功能完全无法使用，后端API缺失

**建议方案：**

#### 方案1：立即禁用微信扫码（推荐，快速解决）
**时间：** 0.5天
**影响：** 用户暂时只能使用账号密码登录
```javascript
// 在登录页面临时隐藏微信扫码选项
const showWechatLogin = false; // 设为 false
```

#### 方案2：完整实现微信扫码登录
**时间：** 2-3天
**影响：** 需要配置微信开放平台，实现完整后端API
**风险：** 可能遇到微信配置和审核问题

#### 方案3：使用模拟数据临时解决
**时间：** 1天
**影响：** 开发环境可以测试，生产环境仍有问题
**风险：** 治标不治本

### 推荐执行顺序：
1. **立即执行方案1** - 禁用微信扫码，确保系统可用
2. **并行开发方案2** - 后续实现完整微信登录
3. **完成其他认证功能** - 注册、找回密码等

### 需要确认的问题：
- [ ] 是否有微信开放平台账号？
- [ ] 是否需要立即支持微信扫码登录？
- [ ] 用户是否可以接受暂时只用账号密码登录？

---

**最后更新：** 2025-07-31
**负责人：** 开发团队
**审核人：** 项目经理
